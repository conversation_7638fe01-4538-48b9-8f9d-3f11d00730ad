# Files (copy each file exactly into a file of that name)

## `index.html`

```html
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>AI-Powered Narrative Video — Personal Blueprint</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container">
      <h1>AI-Powered Narrative Video — Personal Blueprint</h1>
      <nav>
        <a href="index.html">Home</a>
        <a href="about.html">India Patterns & Sources</a>
        <a href="tools.html">AI Tools</a>
        <a href="tracker.html">Practice Tracker</a>
        <a href="outreach.html">Client Outreach</a>
        <a href="portfolio.html">Portfolio Organizer</a>
      </nav>
    </div>
  </header>

  <main class="container">
    <section class="hero">
      <h2>Turn 21+ years of editing into a remote, AI-driven career</h2>
      <p>Fast, practical roadmap with India context — start small, secure recurring income, protect work-life balance.</p>
      <div class="cta-row">
        <a class="btn" href="tracker.html">Start AI Practice</a>
        <a class="btn ghost" href="outreach.html">Begin Client Outreach</a>
      </div>
    </section>

    <section class="quick-steps">
      <h3>90-day plan (at a glance)</h3>
      <ol>
        <li><strong>Week 1–2:</strong> Pick 2 AI tools, make 3 demo pieces (1-min ad, explainer, repurposed reel).</li>
        <li><strong>Week 3–6:</strong> Launch profiles, pitch 10 creators, land first paid client.</li>
        <li><strong>Month 2–3:</strong> Package services and convert to 1–2 retainers.</li>
      </ol>
      <p class="note">See <a href="about.html">India Patterns & Sources</a> for evidence and links to help target local OTT / SME clients.</p>
    </section>

    <section class="features">
      <article>
        <h4>Practice Tracker</h4>
        <p>Daily 30-min AI tool practice with completion tracking (open the tracker page).</p>
      </article>
      <article>
        <h4>Client Outreach Log</h4>
        <p>Simple CRM to track outreach messages, replies, outcomes.</p>
      </article>
      <article>
        <h4>Income & Targets</h4>
        <p>Chart your monthly goals and progress. (Open the tracker.)</p>
      </article>
    </section>

    <section class="resources">
      <h3>Fast links (India-centred)</h3>
      <ul>
        <li><a href="about.html">India Adoption & Market Sources</a> — evidence & links</li>
        <li><a href="tools.html">Suggested AI Tools & Tutorials</a></li>
        <li><a href="https://www.upwork.com" target="_blank">Upwork</a> — global gigs</li>
        <li><a href="https://www.freelancer.com" target="_blank">Freelancer.com</a> — India-heavy listings</li>
      </ul>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <small>Private local site • Data stored in browser (localStorage) • Sources linked on India page.</small>
    </div>
  </footer>

  <script src="app.js"></script>
</body>
</html>
```

---

## `about.html`

```html
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>India Patterns & Sources — AI Video Career</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container">
      <h1>India Patterns & Sources</h1>
      <nav>
        <a href="index.html">Home</a>
        <a href="about.html">India Patterns</a>
        <a href="tools.html">AI Tools</a>
        <a href="tracker.html">Practice Tracker</a>
      </nav>
    </div>
  </header>

  <main class="container">
    <section>
      <h2>Key India trends (summary)</h2>
      <ul>
        <li><strong>High consumer expectations for generative AI</strong> — Indian audiences and brands expect AI adoption quickly. <a href="https://www.adobe.com/content/dam/cc/in/about-adobe/newsroom/pdfs/2025/Press%20Release_Indian%20Consumers%20Expect%20Brands%20to%20Maximise%20the%20Value%20Exchange%20with%20Generative%20AI_Adobe%20Study.pdf" target="_blank">Adobe study (2025)</a>.</li>
        <li><strong>Adobe and other vendors are shipping video AI</strong> — Firefly Video model announcements and Firefly availability mean you can integrate AI into Adobe workflows. <a href="https://www.adobe.com/products/firefly.html" target="_blank">Adobe Firefly</a> and <a href="https://www.indiatoday.in/technology/news/story/adobe-introduces-firefly-ai-video-generator-with-new-tool-calls-it-first-commercially-safe-model-2616957-2024-10-15" target="_blank">IndiaToday on Firefly</a>.</li>
        <li><strong>OTT & studios in India use AI to cut costs</strong> — AI being used for scripts and post-production, creating opportunities for AI-savvy editors. <a href="https://www.exchange4media.com/digital-news/ott-platforms-tap-ai-for-scripts-videos-and-virtual-sets-to-cut-costs-144349.html" target="_blank">Exchange4Media</a>.</li>
        <li><strong>Home post-production trend</strong> — several regional Indian industries are moving post into home studios. <a href="https://timesofindia.indiatimes.com/city/kochi/now-trending-in-kerala-post-production-at-home-personal-spaces/articleshow/121887753.cms" target="_blank">Times of India</a>.</li>
        <li><strong>Freelance marketplace data</strong> — global platforms (Upwork) show a wide rate range; local articles provide salary context for India. See <a href="https://www.upwork.com/hire/video-editors/cost/" target="_blank">Upwork rates</a> and an India-centric freelance earnings overview on UpGrad. </li>
      </ul>
    </section>

    <section>
      <h2>How to use these patterns</h2>
      <ol>
        <li>Target local OTT/regional creators + global creators (dual market).</li>
        <li>Emphasize Adobe + Runway / Descript skills in proposals (tools the market recognises).</li>
        <li>Offer cost/time-saving AI workflows as a selling point to Indian SMEs/OTT who want lower production cost.</li>
      </ol>
    </section>

    <section class="links">
      <h3>Source links (quick)</h3>
      <ul>
        <li><a href="https://www.adobe.com/content/dam/cc/in/about-adobe/newsroom/pdfs/2025/Press%20Release_Indian%20Consumers%20Expect%20Brands%20to%20Maximise%20the%20Value%20Exchange%20with%20Generative%20AI_Adobe%20Study.pdf" target="_blank">Adobe: India generative AI study (2025)</a></li>
        <li><a href="https://www.adobe.com/products/firefly.html" target="_blank">Adobe Firefly</a></li>
        <li><a href="https://www.indiatoday.in/technology/news/story/adobe-introduces-firefly-ai-video-generator-with-new-tool-calls-it-first-commercially-safe-model-2616957-2024-10-15" target="_blank">IndiaToday: Firefly Video (coverage)</a></li>
        <li><a href="https://www.exchange4media.com/digital-news/ott-platforms-tap-ai-for-scripts-videos-and-virtual-sets-to-cut-costs-144349.html" target="_blank">Exchange4Media: OTT + AI</a></li>
        <li><a href="https://timesofindia.indiatimes.com/city/kochi/now-trending-in-kerala-post-production-at-home-personal-spaces/articleshow/121887753.cms" target="_blank">Times of India: Home post-production</a></li>
        <li><a href="https://www.upwork.com/hire/video-editors/cost/" target="_blank">Upwork: rate guidance</a></li>
        <li><a href="https://www.upgrad.com/blog/highest-paying-freelancing-jobs-india/" target="_blank">UpGrad: freelance pay in India</a></li>
      </ul>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <small>Back to <a href="index.html">Home</a></small>
    </div>
  </footer>

  <script src="app.js"></script>
</body>
</html>
```

---

## `tools.html`

```html
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>AI Tools & Learning Paths</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container">
      <h1>AI Tools & Learning Paths</h1>
      <nav>
        <a href="index.html">Home</a>
        <a href="about.html">India Patterns</a>
        <a href="tracker.html">Practice Tracker</a>
      </nav>
    </div>
  </header>

  <main class="container">
    <section>
      <h2>Starter Stack (pick 2)</h2>
      <ul>
        <li><strong>Descript</strong> — fast transcript-based editing & filler removal. <a href="https://www.descript.com" target="_blank">descript.com</a></li>
        <li><strong>Runway</strong> — Gen models for visuals, background removal, motion tools. <a href="https://runwayml.com" target="_blank">runwayml.com</a></li>
        <li><strong>Adobe Firefly / After Effects</strong> — ideation + motion graphics integration. <a href="https://www.adobe.com/products/firefly.html" target="_blank">Adobe Firefly</a></li>
        <li><strong>ChatGPT / Claude</strong> — script & thumbnail text ideation.</li>
        <li><strong>ElevenLabs / Descript (VO)</strong> — voiceovers when needed.</li>
      </ul>
    </section>

    <section>
      <h2>Suggested learning path (6–8 weeks)</h2>
      <ol>
        <li>Week 1–2: Descript basics — transcript editing & multicam workflows.</li>
        <li>Week 3–4: Runway for B-roll & motion fills; test image→video generative features.</li>
        <li>Week 5–6: AE + Firefly prompts for rapid motion graphic concepts.</li>
      </ol>
      <p class="note">Use the <a href="tracker.html">Practice Tracker</a> to log focused 30-minute sessions for each tool.</p>
    </section>

    <section class="links">
      <h3>Tutorial Hubs</h3>
      <ul>
        <li><a href="https://www.adobe.com" target="_blank">Adobe Tutorials</a></li>
        <li><a href="https://runwayml.com" target="_blank">Runway Docs</a></li>
        <li><a href="https://www.youtube.com" target="_blank">YouTube — targeted tool tutorials</a></li>
        <li><a href="https://bitsi.in/top-ai-tools-for-content-creators-from-ideas-to-publishing/" target="_blank">India: AI tools guide (bitsi.in)</a></li>
      </ul>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <small>Back to <a href="index.html">Home</a></small>
    </div>
  </footer>

  <script src="app.js"></script>
</body>
</html>
```

---

## `tracker.html`

```html
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Practice Tracker & Income Chart</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container">
      <h1>Practice Tracker & Income Goals</h1>
      <nav>
        <a href="index.html">Home</a>
        <a href="tools.html">Tools</a>
        <a href="outreach.html">Client Outreach</a>
      </nav>
    </div>
  </header>

  <main class="container">
    <section class="tracker-panel">
      <h2>AI Tool Practice</h2>
      <form id="practiceForm">
        <label>Tool name <input id="toolName" required /></label>
        <label>Minutes practiced <input id="minutes" type="number" min="10" max="240" required /></label>
        <label>Notes <input id="practiceNotes" /></label>
        <button type="submit" class="btn">Add Practice</button>
      </form>

      <h3>Recent sessions</h3>
      <ul id="practiceList" class="list"></ul>
    </section>

    <section class="income-panel">
      <h2>Income Goal Chart</h2>
      <div class="chart-wrap">
        <canvas id="incomeChart" width="600" height="300"></canvas>
      </div>

      <form id="incomeForm" class="inline-form">
        <label>Month <input id="monthLabel" placeholder="e.g., 2025-08" required /></label>
        <label>Goal (₹) <input id="goalAmount" type="number" required /></label>
        <label>Actual (₹) <input id="actualAmount" type="number" required /></label>
        <button class="btn" id="addIncome">Add</button>
      </form>

      <ul id="incomeList" class="list"></ul>
    </section>

  </main>

  <footer class="site-footer">
    <div class="container">
      <small>Data saved to browser. Export/backup using the Export button below.</small>
      <div class="controls">
        <button id="exportBtn" class="btn ghost">Export JSON</button>
        <button id="importBtn" class="btn ghost">Import JSON</button>
        <input id="importFile" type="file" accept=".json" style="display:none" />
      </div>
    </div>
  </footer>

  <!-- Chart.js CDN (online) -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="app.js"></script>
  <script>
    // initialize chart after app.js sets up data functions
    document.addEventListener('DOMContentLoaded', () => {
      if (window.startIncomeChart) window.startIncomeChart();
    });
  </script>
</body>
</html>
```

---

## `outreach.html`

```html
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Client Outreach Log</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container">
      <h1>Client Outreach Log</h1>
      <nav>
        <a href="index.html">Home</a>
        <a href="tracker.html">Tracker</a>
        <a href="portfolio.html">Portfolio</a>
      </nav>
    </div>
  </header>

  <main class="container">
    <section>
      <h2>Add Outreach</h2>
      <form id="outreachForm">
        <label>Contact Name / Handle <input id="contactName" required /></label>
        <label>Platform (LinkedIn/Upwork/Email) <input id="platform" /></label>
        <label>Message sent <textarea id="messageSent"></textarea></label>
        <label>Outcome <select id="outcome">
          <option>Pending</option><option>Replied</option><option>Interview</option><option>Won</option><option>Lost</option>
        </select></label>
        <button class="btn" type="submit">Add Contact</button>
      </form>

      <h3>Outreach history</h3>
      <ul id="outreachList" class="list"></ul>
    </section>

    <section>
      <h3>Pitch templates</h3>
      <p>Quick templates: use these in LinkedIn/Upwork messages and personalize.</p>
      <details>
        <summary>Short cold pitch (LinkedIn)</summary>
        <pre>Hi [Name], I specialise in AI-enhanced narrative edits for creators & small brands. I can turn a 10-min recording into 3X social clips/week. Would you like a 7-day trial edit?</pre>
      </details>
      <details>
        <summary>Upwork proposal snippet</summary>
        <pre>I bring 21+ years of editing & motion graphics. I use Descript/Runway/After Effects workflows to deliver polished narrative videos faster. Portfolio: [link]. $[rate] for a sample edit.</pre>
      </details>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <small>All data saved to browser storage.</small>
    </div>
  </footer>

  <script src="app.js"></script>
</body>
</html>
```

---

## `portfolio.html`

```html
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <title>Portfolio Organizer</title>
  <link rel="stylesheet" href="styles.css" />
</head>
<body>
  <header class="site-header">
    <div class="container">
      <h1>Portfolio Organizer</h1>
      <nav>
        <a href="index.html">Home</a>
        <a href="outreach.html">Outreach</a>
        <a href="tracker.html">Tracker</a>
      </nav>
    </div>
  </header>

  <main class="container">
    <section>
      <h2>Add Portfolio Item</h2>
      <form id="portfolioForm">
        <label>Title <input id="pfTitle" required /></label>
        <label>Type <input id="pfType" placeholder="e.g., ad intro, explainer" /></label>
        <label>Tools used <input id="pfTools" placeholder="e.g., Descript, Runway, AE" /></label>
        <label>Local file link / public URL <input id="pfLink" /></label>
        <label>Notes <input id="pfNotes" /></label>
        <button class="btn" type="submit">Add</button>
      </form>

      <h3>Your items</h3>
      <ul id="portfolioList" class="list"></ul>
    </section>

    <section>
      <h3>Export Options</h3>
      <p>Export your portfolio items as JSON for backup or to attach to proposals.</p>
      <button id="exportPortfolio" class="btn ghost">Export Portfolio JSON</button>
    </section>
  </main>

  <footer class="site-footer">
    <div class="container">
      <small>Use local file links (file://) or public URLs for demos.</small>
    </div>
  </footer>

  <script src="app.js"></script>
</body>
</html>
```

---

## `styles.css`

```css
:root{
  --bg:#0f1724;
  --card:#0b1220;
  --accent:#00d1b2;
  --muted:#98a0b3;
  --glass: rgba(255,255,255,0.03);
  --maxw:1100px;
  font-family: Inter, system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial;
}

*{box-sizing:border-box}
body{
  margin:0;
  background: linear-gradient(180deg,#061027 0%, #0b1220 100%);
  color:#e6eef6;
  line-height:1.45;
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale;
}

.container{
  max-width:var(--maxw);
  margin:0 auto;
  padding:24px;
}

.site-header{
  border-bottom:1px solid rgba(255,255,255,0.04);
  background: linear-gradient(90deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01));
}
.site-header .container{display:flex;align-items:center;gap:24px}
.site-header h1{font-size:1.05rem;margin:12px 0}
nav{margin-left:auto}
nav a{color:var(--muted);text-decoration:none;margin-left:14px;font-weight:600}
nav a:hover{color:var(--accent)}

.hero{
  background:var(--glass);
  border-radius:12px;
  padding:18px;
  margin-top:18px;
  box-shadow: 0 6px 20px rgba(1,6,15,0.6);
}
.hero h2{margin:6px 0 8px 0}
.cta-row{margin-top:12px}
.btn{
  display:inline-block;
  background:var(--accent);
  color:#042126;
  padding:10px 14px;
  border-radius:10px;
  text-decoration:none;
  font-weight:700;
  margin-right:8px;
  cursor:pointer;
  border: none;
}
.btn.ghost{background:transparent;color:var(--accent);border:1px solid rgba(255,255,255,0.04)}

.quick-steps, .features{margin-top:18px;background:rgba(255,255,255,0.02);padding:14px;border-radius:10px}
.note{color:var(--muted);font-size:0.95rem}

.features{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:12px}
.features article{padding:12px;border-radius:8px;background:linear-gradient(180deg, rgba(255,255,255,0.01), rgba(255,255,255,0.005))}

.site-footer{padding:18px;border-top:1px solid rgba(255,255,255,0.02);margin-top:20px}
.list{list-style:none;padding:0;margin:12px 0}
.list li{background:rgba(255,255,255,0.02);padding:10px;border-radius:8px;margin-bottom:8px;color:var(--muted);font-size:0.95rem}

.form-row{display:flex;gap:8px;align-items:center}
.inline-form label{display:inline-block;margin-right:8px}
input,textarea,select{
  background:#071126;border:1px solid rgba(255,255,255,0.03);color:#cfeff0;padding:8px;border-radius:6px;width:100%;
}
textarea{min-height:64px}
.controls{display:flex;gap:8px;align-items:center;margin-top:8px}

@media (max-width:720px){
  .site-header .container{flex-direction:column;align-items:flex-start}
  nav{margin-left:0}
  .features{grid-template-columns:1fr}
}
```

---

## `app.js`

```javascript
/* app.js
 Simple localStorage-based app for tracker, outreach, portfolio, income chart
 Save this file in the same folder. */

const Storage = {
  get(key, fallback){ try{ return JSON.parse(localStorage.getItem(key)) || fallback }catch(e){return fallback} },
  set(key, val){ localStorage.setItem(key, JSON.stringify(val)) },
  clear(key){ localStorage.removeItem(key) }
};

// PRACTICE TRACKER
function addPracticeEntry({tool, minutes, notes}){
  const list = Storage.get('practice', []);
  list.unshift({tool, minutes, notes, ts: Date.now()});
  Storage.set('practice', list.slice(0,200)); // keep small
}
function renderPractice(){
  const el = document.getElementById('practiceList');
  if(!el) return;
  const list = Storage.get('practice', []);
  el.innerHTML = list.map(i=>`<li><strong>${i.tool}</strong> — ${i.minutes} min <div class="muted">${new Date(i.ts).toLocaleString()}</div><div>${i.notes||''}</div></li>`).join('');
}
document.addEventListener('submit', e=>{
  if(e.target && e.target.id==='practiceForm'){
    e.preventDefault();
    const tool = document.getElementById('toolName').value.trim();
    const minutes = Number(document.getElementById('minutes').value);
    const notes = document.getElementById('practiceNotes').value.trim();
    addPracticeEntry({tool, minutes, notes});
    e.target.reset();
    renderPractice();
  }
});

// OUTREACH
function addContact(contact){
  const list = Storage.get('outreach', []);
  list.unshift({...contact, ts: Date.now()});
  Storage.set('outreach', list.slice(0,500));
}
function renderOutreach(){
  const el = document.getElementById('outreachList');
  if(!el) return;
  const list = Storage.get('outreach', []);
  el.innerHTML = list.map(c=>`<li><strong>${c.contactName}</strong> [${c.platform||'—'}] <div>${c.messageSent||''}</div><div class="muted">${c.outcome||'Pending'} • ${new Date(c.ts).toLocaleDateString()}</div></li>`).join('');
}
document.addEventListener('submit', e=>{
  if(e.target && e.target.id==='outreachForm'){
    e.preventDefault();
    addContact({
      contactName: document.getElementById('contactName').value.trim(),
      platform: document.getElementById('platform').value.trim(),
      messageSent: document.getElementById('messageSent').value.trim(),
      outcome: document.getElementById('outcome').value
    });
    e.target.reset();
    renderOutreach();
  }
});

// PORTFOLIO
function addPortfolio(item){
  const list = Storage.get('portfolio', []);
  list.unshift(item);
  Storage.set('portfolio', list.slice(0,200));
}
function renderPortfolio(){
  const el = document.getElementById('portfolioList');
  if(!el) return;
  const list = Storage.get('portfolio', []);
  el.innerHTML = list.map(p=>`<li><strong>${p.title}</strong> <div>${p.type||''} • ${p.tools||''}</div><div><a href="${p.link||'#'}" target="_blank">${p.link||'—'}</a></div><div class="muted">${p.notes||''}</div></li>`).join('');
}
document.addEventListener('submit', e=>{
  if(e.target && e.target.id==='portfolioForm'){
    e.preventDefault();
    addPortfolio({
      title: document.getElementById('pfTitle').value.trim(),
      type: document.getElementById('pfType').value.trim(),
      tools: document.getElementById('pfTools').value.trim(),
      link: document.getElementById('pfLink').value.trim(),
      notes: document.getElementById('pfNotes').value.trim()
    });
    e.target.reset();
    renderPortfolio();
  }
});

// INCOME CHART
let incomeChart = null;
function startIncomeChart(){
  const ctx = document.getElementById('incomeChart');
  if(!ctx) return;
  const incomeData = Storage.get('income', []);
  const labels = incomeData.map(i=>i.month).reverse();
  const goals = incomeData.map(i=>i.goal).reverse();
  const actuals = incomeData.map(i=>i.actual).reverse();
  incomeChart = new Chart(ctx.getContext('2d'), {
    type: 'line',
    data: {
      labels,
      datasets: [
        {label:'Goal (₹)', data:goals, fill:false, tension:0.3},
        {label:'Actual (₹)', data:actuals, fill:true, tension:0.3}
      ]
    },
    options:{
      scales: { y:{ beginAtZero:true } }
    }
  });
  renderIncomeList();
}
function addIncome(month, goal, actual){
  const list = Storage.get('income', []);
  list.unshift({month, goal:Number(goal), actual:Number(actual)});
  Storage.set('income', list.slice(0,24));
  if(incomeChart) incomeChart.destroy();
  startIncomeChart();
}
function renderIncomeList(){
  const el = document.getElementById('incomeList');
  if(!el) return;
  const list = Storage.get('income', []);
  el.innerHTML = list.map(i=>`<li><strong>${i.month}</strong> • Goal ₹${i.goal} • Actual ₹${i.actual}</li>`).join('');
}
document.addEventListener('submit', e=>{
  if(e.target && e.target.id==='incomeForm'){
    e.preventDefault();
    addIncome(
      document.getElementById('monthLabel').value.trim(),
      document.getElementById('goalAmount').value,
      document.getElementById('actualAmount').value
    );
    e.target.reset();
    renderIncomeList();
  }
});

// EXPORT / IMPORT
document.addEventListener('DOMContentLoaded', ()=>{
  renderPractice(); renderOutreach(); renderPortfolio(); renderIncomeList();
  // export/import handlers
  const exportBtn = document.getElementById('exportBtn');
  if(exportBtn) exportBtn.addEventListener('click', ()=>{
    const data = {
      practice: Storage.get('practice',[]),
      outreach: Storage.get('outreach',[]),
      portfolio: Storage.get('portfolio',[]),
      income: Storage.get('income',[])
    };
    const blob = new Blob([JSON.stringify(data, null, 2)],{type:'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a'); a.href = url; a.download = 'career-data.json'; a.click();
    URL.revokeObjectURL(url);
  });
  const importBtn = document.getElementById('importBtn');
  const importFile = document.getElementById('importFile');
  if(importBtn) importBtn.addEventListener('click', ()=> importFile.click());
  if(importFile) importFile.addEventListener('change', (ev)=>{
    const f = ev.target.files[0];
    if(!f) return;
    const reader = new FileReader();
    reader.onload = ()=>{
      try{
        const parsed = JSON.parse(reader.result);
        if(parsed.practice) Storage.set('practice', parsed.practice);
        if(parsed.outreach) Storage.set('outreach', parsed.outreach);
        if(parsed.portfolio) Storage.set('portfolio', parsed.portfolio);
        if(parsed.income) Storage.set('income', parsed.income);
        renderPractice(); renderOutreach(); renderPortfolio(); renderIncomeList();
        if(incomeChart) incomeChart.destroy(); startIncomeChart();
        alert('Import complete');
      }catch(e){ alert('Invalid file'); }
    };
    reader.readAsText(f);
  });

  // portfolio export
  const exportPortfolio = document.getElementById('exportPortfolio');
  if(exportPortfolio) exportPortfolio.addEventListener('click', ()=>{
    const data = Storage.get('portfolio',[]);
    const blob = new Blob([JSON.stringify(data, null, 2)],{type:'application/json'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a'); a.href = url; a.download = 'portfolio.json'; a.click();
    URL.revokeObjectURL(url);
  });
});

// Misc: fallback chart initializer for pages without Chart.js loaded gracefully
window.startIncomeChart = function(){
  try{
    if(typeof Chart !== 'undefined') startIncomeChart();
    else {
      // placeholder – render plain list
      renderIncomeList();
    }
  }catch(e){
    console.warn('Chart error', e);
  }
};
```

---

### Notes & usage

* The pages use `localStorage` to save all entries (practice sessions, outreach log, portfolio items, income rows). This keeps everything private in the browser.
* Charting uses Chart.js CDN (included in tracker page). If you need fully offline, I can produce a tiny pure-JS chart instead — just say so and I’ll replace it.
* Links in `about.html` point to the sources used in research (Adobe, IndiaToday, Exchange4Media, Times of India, Upwork, UpGrad). Those open in the browser for deeper reading.

---